
dataset_info = dict(
    dataset_name='custom_hand',
    paper_info=dict(
        author='Your Name',
        title='Your Dataset',
        container='Your Container',
        year='2024',
        homepage='Your Homepage',
    ),
    keypoint_info={
        0:
        dict(
            name='WRIST',
            id=0,
            color=[255, 255, 255],
            type='bone',
            swap=''),
        1:
        dict(
            name='THUMB_CMC',
            id=1,
            color=[255, 255, 255],
            type='bone',
            swap=''),
        2:
        dict(
            name='THUMB_MCP',
            id=2,
            color=[255, 255, 255],
            type='bone',
            swap=''),
        3:
        dict(
            name='THUMB_IP',
            id=3,
            color=[255, 255, 255],
            type='bone',
            swap=''),
        4:
        dict(
            name='THUMB_TIP',
            id=4,
            color=[255, 255, 255],
            type='bone',
            swap=''),
        5:
        dict(
            name='INDEX_FINGER_MCP',
            id=5,
            color=[255, 255, 255],
            type='bone',
            swap=''),
        6:
        dict(
            name='INDEX_FINGER_PIP',
            id=6,
            color=[255, 255, 255],
            type='bone',
            swap=''),
        7:
        dict(
            name='INDEX_FINGER_DIP',
            id=7,
            color=[255, 255, 255],
            type='bone',
            swap=''),
        8:
        dict(
            name='INDEX_FINGER_TIP',
            id=8,
            color=[255, 255, 255],
            type='bone',
            swap=''),
        9:
        dict(
            name='MIDDLE_FINGER_MCP',
            id=9,
            color=[255, 255, 255],
            type='bone',
            swap=''),
        10:
        dict(
            name='MIDDLE_FINGER_PIP',
            id=10,
            color=[255, 255, 255],
            type='bone',
            swap=''),
        11:
        dict(
            name='MIDDLE_FINGER_DIP',
            id=11,
            color=[255, 255, 255],
            type='bone',
            swap=''),
        12:
        dict(
            name='MIDDLE_FINGER_TIP',
            id=12,
            color=[255, 255, 255],
            type='bone',
            swap=''),
        13:
        dict(
            name='RING_FINGER_MCP',
            id=13,
            color=[255, 255, 255],
            type='bone',
            swap=''),
        14:
        dict(
            name='RING_FINGER_PIP',
            id=14,
            color=[255, 255, 255],
            type='bone',
            swap=''),
        15:
        dict(
            name='RING_FINGER_DIP',
            id=15,
            color=[255, 255, 255],
            type='bone',
            swap=''),
        16:
        dict(
            name='RING_FINGER_TIP',
            id=16,
            color=[255, 255, 255],
            type='bone',
            swap=''),
        17:
        dict(
            name='PINKY_MCP',
            id=17,
            color=[255, 255, 255],
            type='bone',
            swap=''),
        18:
        dict(
            name='PINKY_PIP',
            id=18,
            color=[255, 255, 255],
            type='bone',
            swap=''),
        19:
        dict(
            name='PINKY_DIP',
            id=19,
            color=[255, 255, 255],
            type='bone',
            swap=''),
        20:
        dict(
            name='PINKY_TIP',
            id=20,
            color=[255, 255, 255],
            type='bone',
            swap='')
    },
    skeleton_info={
        0:
        dict(link=('WRIST', 'THUMB_CMC'), id=0, color=[255, 255, 255]),
        1:
        dict(link=('THUMB_CMC', 'THUMB_MCP'), id=1, color=[255, 255, 255]),
        2:
        dict(link=('THUMB_MCP', 'THUMB_IP'), id=2, color=[255, 255, 255]),
        3:
        dict(link=('THUMB_IP', 'THUMB_TIP'), id=3, color=[255, 255, 255]),
        4:
        dict(link=('WRIST', 'INDEX_FINGER_MCP'), id=4, color=[255, 255, 255]),
        5:
        dict(
            link=('INDEX_FINGER_MCP', 'INDEX_FINGER_PIP'),
            id=5,
            color=[255, 255, 255]),
        6:
        dict(
            link=('INDEX_FINGER_PIP', 'INDEX_FINGER_DIP'),
            id=6,
            color=[255, 255, 255]),
        7:
        dict(
            link=('INDEX_FINGER_DIP', 'INDEX_FINGER_TIP'),
            id=7,
            color=[255, 255, 255]),
        8:
        dict(link=('WRIST', 'MIDDLE_FINGER_MCP'), id=8, color=[255, 255, 255]),
        9:
        dict(
            link=('MIDDLE_FINGER_MCP', 'MIDDLE_FINGER_PIP'),
            id=9,
            color=[255, 255, 255]),
        10:
        dict(
            link=('MIDDLE_FINGER_PIP', 'MIDDLE_FINGER_DIP'),
            id=10,
            color=[255, 255, 255]),
        11:
        dict(
            link=('MIDDLE_FINGER_DIP', 'MIDDLE_FINGER_TIP'),
            id=11,
            color=[255, 255, 255]),
        12:
        dict(link=('WRIST', 'RING_FINGER_MCP'), id=12, color=[255, 255, 255]),
        13:
        dict(
            link=('RING_FINGER_MCP', 'RING_FINGER_PIP'),
            id=13,
            color=[255, 255, 255]),
        14:
        dict(
            link=('RING_FINGER_PIP', 'RING_FINGER_DIP'),
            id=14,
            color=[255, 255, 255]),
        15:
        dict(
            link=('RING_FINGER_DIP', 'RING_FINGER_TIP'),
            id=15,
            color=[255, 255, 255]),
        16:
        dict(link=('WRIST', 'PINKY_MCP'), id=16, color=[255, 255, 255]),
        17:
        dict(link=('PINKY_MCP', 'PINKY_PIP'), id=17, color=[255, 255, 255]),
        18:
        dict(link=('PINKY_PIP', 'PINKY_DIP'), id=18, color=[255, 255, 255]),
        19:
        dict(link=('PINKY_DIP', 'PINKY_TIP'), id=19, color=[255, 255, 255]),
        20:
        dict(link=('INDEX_FINGER_MCP', 'MIDDLE_FINGER_MCP'), id=20, color=[255, 255, 255]),
        21:
        dict(link=('MIDDLE_FINGER_MCP', 'RING_FINGER_MCP'), id=21, color=[255, 255, 255]),
        22:
        dict(link=('RING_FINGER_MCP', 'PINKY_MCP'), id=22, color=[255, 255, 255]),
    },
    joint_weights=[
        1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1.,
        1., 1., 1., 1.
    ],
    sigmas=[
        0.025, 0.025, 0.025, 0.025, 0.025, 0.025, 0.025, 0.025, 0.025, 0.025,
        0.025, 0.025, 0.025, 0.025, 0.025, 0.025, 0.025, 0.025, 0.025, 0.025,
        0.025
    ])
