dataset_info = dict(
    dataset_name='custom_hand_acu23',
    paper_info=dict(
        author='Your Name',
        title='Custom Hand Acupoint Dataset',
        container='Your Container',
        year='2024',
        homepage='Your Homepage',
    ),
    # 23 hand acupoints
    keypoint_info={
        0: dict(name='Shaoshang', id=0, color=[255, 0, 0], type='acupoint', swap=''),
        1: dict(name='Yuji', id=1, color=[255, 0, 0], type='acupoint', swap=''),
        2: dict(name='Taiyuan', id=2, color=[255, 0, 0], type='acupoint', swap=''),
        3: dict(name='Daling', id=3, color=[255, 0, 0], type='acupoint', swap=''),
        4: dict(name='Laogong', id=4, color=[255, 0, 0], type='acupoint', swap=''),
        5: dict(name='Zhongchong', id=5, color=[255, 0, 0], type='acupoint', swap=''),
        6: dict(name='Shenmen', id=6, color=[255, 0, 0], type='acupoint', swap=''),
        7: dict(name='<PERSON><PERSON>fu', id=7, color=[255, 0, 0], type='acupoint', swap=''),
        8: dict(name='Shaochong', id=8, color=[255, 0, 0], type='acupoint', swap=''),
        9: dict(name='Shangyang', id=9, color=[255, 0, 0], type='acupoint', swap=''),
        10: dict(name='Erjian', id=10, color=[255, 0, 0], type='acupoint', swap=''),
        11: dict(name='Sanjian', id=11, color=[255, 0, 0], type='acupoint', swap=''),
        12: dict(name='Hegu', id=12, color=[255, 0, 0], type='acupoint', swap=''),
        13: dict(name='Yangxi', id=13, color=[255, 0, 0], type='acupoint', swap=''),
        14: dict(name='Shaoze', id=14, color=[255, 0, 0], type='acupoint', swap=''),
        15: dict(name='Qiangu', id=15, color=[255, 0, 0], type='acupoint', swap=''),
        16: dict(name='Houxi', id=16, color=[255, 0, 0], type='acupoint', swap=''),
        17: dict(name='Wangu', id=17, color=[255, 0, 0], type='acupoint', swap=''),
        18: dict(name='Yanggu', id=18, color=[255, 0, 0], type='acupoint', swap=''),
        19: dict(name='Guanchong', id=19, color=[255, 0, 0], type='acupoint', swap=''),
        20: dict(name='Yemen', id=20, color=[255, 0, 0], type='acupoint', swap=''),
        21: dict(name='Zhongzhu', id=21, color=[255, 0, 0], type='acupoint', swap=''),
        22: dict(name='Yangchi', id=22, color=[255, 0, 0], type='acupoint', swap=''),
    },
    # Define symmetric flip pairs (self-map if no clear symmetry)
    # Required to generate correct flip_indices to avoid IndexError
    flip_pairs=[(i, i) for i in range(23)],
    # No specific skeleton for acupoints
    skeleton_info={},
    # Training weights and sigmas for 23 points
    joint_weights=[
        1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1.,
        1., 1., 1., 1., 1.
    ],
    sigmas=[
        0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01,
        0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01,
        0.01, 0.01, 0.01
    ],
)