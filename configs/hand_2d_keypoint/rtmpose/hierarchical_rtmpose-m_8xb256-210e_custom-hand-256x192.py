_base_ = ['../../_base_/default_runtime.py']

# runtime
max_epochs = 210
stage2_num_epochs = 10
base_lr = 4e-3

train_cfg = dict(max_epochs=max_epochs, val_interval=2, val_begin=2)  # 从第2个epoch开始，每2个epoch验证一次
randomness = dict(seed=21)

# optimizer
optim_wrapper = dict(
    type='OptimWrapper',
    optimizer=dict(type='AdamW', lr=base_lr, weight_decay=0.05),
    paramwise_cfg=dict(
        norm_decay_mult=0, bias_decay_mult=0, bypass_duplicate=True))

# learning rate
param_scheduler = [
    dict(
        type='LinearLR',
        start_factor=1.0e-5,
        by_epoch=False,
        begin=0,
        end=1000),
    dict(
        # use cosine lr from 105 to 210 epoch
        type='CosineAnnealingLR',
        eta_min=base_lr * 0.05,
        begin=max_epochs // 2,
        end=max_epochs,
        T_max=max_epochs // 2,
        by_epoch=True,
        convert_to_iter_based=True),
]

# automatically scaling LR based on the actual training batch size
auto_scale_lr = dict(base_batch_size=1024)

# codec settings for anchors (21 points)
anchor_codec = dict(
    type='SimCCLabel',
    input_size=(192, 256),
    sigma=(5.66, 5.66),
    simcc_split_ratio=2.0,
    normalize=False,
    use_dark=False)

# codec settings for acupoints (23 points)
acupoint_codec = dict(
    type='SimCCLabel',
    input_size=(192, 256),
    sigma=(4.0, 4.0),
    simcc_split_ratio=2.0,
    normalize=False,
    use_dark=False)

# model settings
model = dict(
    type='TopdownPoseEstimator',
    data_preprocessor=dict(
        type='PoseDataPreprocessor',
        mean=[123.675, 116.28, 103.53],
        std=[58.395, 57.12, 57.375],
        bgr_to_rgb=True),
    backbone=dict(
        _scope_='mmdet',
        type='CSPNeXt',
        arch='P5',
        expand_ratio=0.5,
        deepen_factor=0.67,
        widen_factor=0.75,
        out_indices=(4, ),
        channel_attention=True,
        norm_cfg=dict(type='SyncBN'),
        act_cfg=dict(type='SiLU'),
        init_cfg=dict(
            type='Pretrained',
            prefix='backbone.',
            checkpoint='https://download.openmmlab.com/mmpose/v1/projects/'
            'rtmposev1/rtmpose-m_simcc-aic-coco_pt-aic-coco_420e-256x192-63eb25f7_20230126.pth'  # noqa
        )),
    head=dict(
        type='HierarchicalRTMCCHead',
        in_channels=768,
        anchor_out_channels=21,
        acupoint_out_channels=23,
        input_size=anchor_codec['input_size'],
        in_featuremap_size=(6, 8),
        simcc_split_ratio=anchor_codec['simcc_split_ratio'],
        final_layer_kernel_size=7,
        # Anchor decoder config
        anchor_gau_cfg=dict(
            hidden_dims=256,
            s=128,
            expansion_factor=2,
            dropout_rate=0.,
            drop_path=0.,
            act_fn='SiLU',
            use_rel_bias=False,
            pos_enc=False),
        # Acupoint decoder config
        acupoint_gau_cfg=dict(
            hidden_dims=256,
            s=128,
            expansion_factor=2,
            dropout_rate=0.,
            drop_path=0.,
            act_fn='SiLU',
            use_rel_bias=False,
            pos_enc=False),
        # Feature fusion config
        fusion_cfg=dict(
            fusion_type='attention',
            reduction_ratio=4),
        # Coordinate to heatmap conversion
        coord2heatmap_cfg=dict(
            sigma=2.0),
        # Disable anchor prediction and fusion
        use_anchor_prediction=False,
        use_anchor_fusion=False,
        # Disable custom HierarchicalLoss to restore normal anchor/acupoint losses
        hierarchical_loss=dict(
            type='HierarchicalLoss',
            anchor_weight=0.3,
            acupoint_weight=1.0,
            anchor_loss_cfg=dict(
                type='KLDiscretLoss',
                use_target_weight=True,
                beta=10.,
                label_softmax=True),
            acupoint_loss_cfg=dict(
                type='KLDiscretLoss',
                use_target_weight=True,
                beta=6.,
                label_softmax=True)),
        # Alternative: Adaptive Hierarchical Loss (uncomment to use)
        # hierarchical_loss=dict(
        #     type='AdaptiveHierarchicalLoss',
        #     initial_anchor_weight=0.8,    # Start with higher anchor weight
        #     final_anchor_weight=0.2,      # End with lower anchor weight
        #     initial_acupoint_weight=0.5,  # Start with lower acupoint weight
        #     final_acupoint_weight=1.0,    # End with higher acupoint weight
        #     transition_epochs=50,         # Transition over 50 epochs
        #     anchor_loss_cfg=dict(
        #         type='KLDiscretLoss',
        #         use_target_weight=True,
        #         beta=10.,
        #         label_softmax=True),
        #     acupoint_loss_cfg=dict(
        #         type='KLDiscretLoss',
        #         use_target_weight=True,
        #         beta=10.,
        #         label_softmax=True)),
        # Individual loss configs (used when hierarchical_loss is None)
        # Decoders
        anchor_decoder=anchor_codec,
        acupoint_decoder=acupoint_codec),
    test_cfg=dict(flip_test=True))

# base dataset settings
dataset_type = 'HierarchicalHandDataset'
data_mode = 'topdown'
data_root = '/root/autodl-tmp/datasets/mix/'

backend_args = dict(backend='local')

# pipelines
train_pipeline = [
    dict(type='LoadImage', backend_args=backend_args),
    dict(type='GetBBoxCenterScale'),
    dict(type='RandomFlip', direction='horizontal'),
    dict(
        type='RandomBBoxTransform',
        shift_factor=0.,
        scale_factor=[0.75, 1.25],
        rotate_factor=60),
    dict(type='TopdownAffine', input_size=anchor_codec['input_size']),
    dict(type='HierarchicalTopdownAffine'),
    dict(type='mmdet.YOLOXHSVRandomAug'),
    dict(
        type='Albumentation',
        transforms=[
            dict(type='Blur', p=0.1),
            dict(type='MedianBlur', p=0.1),
            dict(
                type='CoarseDropout',
                max_holes=1,
                max_height=0.4,
                max_width=0.4,
                min_holes=1,
                min_height=0.2,
                min_width=0.2,
                p=0.5),
        ]),
    dict(type='HierarchicalGenerateTarget', 
         anchor_encoder=anchor_codec,
         acupoint_encoder=acupoint_codec),
    dict(type='PackPoseInputs')
]

train_pipeline_stage2 = [
    dict(type='LoadImage', backend_args=backend_args),
    dict(type='GetBBoxCenterScale'),
    dict(type='RandomFlip', direction='horizontal'),
    dict(
        type='RandomBBoxTransform',
        shift_factor=0.,
        scale_factor=[0.75, 1.25],
        rotate_factor=60),
    dict(type='TopdownAffine', input_size=anchor_codec['input_size']),
    dict(type='mmdet.YOLOXHSVRandomAug'),
    dict(
        type='Albumentation',
        transforms=[
    dict(type='HierarchicalTopdownAffine'),
            dict(type='Blur', p=0.1),
            dict(type='MedianBlur', p=0.1),
            dict(
                type='CoarseDropout',
                max_holes=1,
                max_height=0.4,
                max_width=0.4,
                min_holes=1,
                min_height=0.2,
                min_width=0.2,
                p=0.5),
        ]),
    dict(type='HierarchicalGenerateTarget', 
         anchor_encoder=anchor_codec,
         acupoint_encoder=acupoint_codec),
    dict(type='PackPoseInputs')
]

# data loaders
train_dataloader = dict(
    batch_size=256,
    num_workers=10,
    persistent_workers=True,
    sampler=dict(type='DefaultSampler', shuffle=True),
    dataset=dict(
        type=dataset_type,
        data_root=data_root,
        data_mode=data_mode,
        ann_file='train_with_mediapipe.json',
        data_prefix=dict(img='images/'),
        load_acupoints=True,  # Enable hierarchical training
        anchor_category_id=2,  # 21 anchor points
        acupoint_category_id=1,  # 23 acupoints
        pipeline=train_pipeline,
    ))

val_pipeline = [
    dict(type='LoadImage', backend_args=backend_args),
    dict(type='GetBBoxCenterScale'),
    dict(type='TopdownAffine', input_size=anchor_codec['input_size']),
    dict(type='PackPoseInputs')
]

val_dataloader = dict(
    batch_size=64,
    num_workers=10,
    persistent_workers=True,
    drop_last=False,
    sampler=dict(type='DefaultSampler', shuffle=False, round_up=False),
    dataset=dict(
        type=dataset_type,
        data_root=data_root,
        data_mode=data_mode,
        ann_file='val_with_mediapipe.json',
        bbox_file=None,
        data_prefix=dict(img='images/'),
        test_mode=True,
        load_acupoints=True,  # Enable hierarchical evaluation
        anchor_category_id=2,  # 21 anchor points
        acupoint_category_id=1,  # 23 acupoints
        pipeline=val_pipeline,
    ))

test_dataloader = val_dataloader

# hooks
default_hooks = dict(
    checkpoint=dict(save_best='coco/AP', rule='greater', max_keep_ckpts=1))

custom_hooks = [
    dict(
        type='EMAHook',
        ema_type='ExpMomentumEMA',
        momentum=0.0002,
        update_buffers=True,
        priority=49),
    dict(
        type='mmdet.PipelineSwitchHook',
        switch_epoch=max_epochs - stage2_num_epochs,
        switch_pipeline=train_pipeline_stage2)
]

# evaluation
# evaluators
val_evaluator = dict(
    type='HierarchicalHandMetric',
    ann_file=data_root + 'val_with_mediapipe.json',
    evaluate_anchors=False,
    evaluate_acupoints=True,
    anchor_category_id=2,
    acupoint_category_id=1)

test_evaluator = val_evaluator 