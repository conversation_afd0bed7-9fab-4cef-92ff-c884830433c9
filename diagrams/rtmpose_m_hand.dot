digraph RTMPOSE_M_HAND {
    rankdir=LR;

    node [shape=record, fontsize=10];

    Input [label="Input Image\n256x192"];
    Preproc [label="PoseDataPreprocessor|mean=[123.675,116.28,103.53]\\lstd=[58.395,57.12,57.375]\\lbgr_to_rgb=True"];

    subgraph cluster_backbone {
        label="Backbone: CSPNeXt (P5, M)";
        style=filled;
        color=lightgrey;
        node [shape=record, style=filled, color=white];

        Stem [label="Stem\\lConv 3x3, 32\\nConv 3x3, 64\\nConv 3x3, 64"];
        Stage1 [label="Stage 1\\lCSP Block x3\\nout 128"];
        Stage2 [label="Stage 2\\lCSP Block x3\\nout 256"];
        Stage3 [label="Stage 3\\lCSP Block x9\\nout 512"];
        Stage4 [label="Stage 4\\lCSP Block x9\\nout 768 (P5)"];
        Stem -> Stage1 -> Stage2 -> Stage3 -> Stage4;
    }

    Head [label="{RTMCCHead|In Channels: 768|Out Channels: 23|Input Size: 256x192|Final Kernel: 7x7|GAU hidden=256 s=128\\lSimCC split=2.0|Loss: KLDiscretLoss(beta=10)}"];

    Output [label="Predicted Joints\\nSimCC Representation"];

    Input -> Preproc -> Stem;
    Stage4 -> Head -> Output;
}